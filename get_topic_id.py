#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取Telegram话题ID的辅助工具
"""

import requests
import json
from datetime import datetime

def get_topic_ids(bot_token):
    """获取最近消息中的话题ID"""
    
    print("🔍 获取Telegram话题ID")
    print("=" * 40)
    
    # 获取最近的更新
    url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            return
        
        data = response.json()
        if not data.get('ok'):
            print(f"❌ API返回错误: {data.get('description')}")
            return
        
        updates = data.get('result', [])
        if not updates:
            print("📭 没有找到最近的消息")
            print("💡 请在目标话题中发送一条消息，然后重新运行此脚本")
            return
        
        print(f"📨 找到 {len(updates)} 条最近消息")
        print("\n📋 话题ID信息:")
        
        topic_ids = {}
        
        for update in updates[-20:]:  # 只查看最近20条
            message = update.get('message')
            if not message:
                continue
            
            chat = message.get('chat', {})
            chat_id = chat.get('id')
            chat_title = chat.get('title', '未知')
            thread_id = message.get('message_thread_id')
            message_text = message.get('text', '')[:50]
            date = datetime.fromtimestamp(message.get('date', 0))
            
            if thread_id:
                key = f"{chat_id}_{thread_id}"
                if key not in topic_ids:
                    topic_ids[key] = {
                        'chat_id': chat_id,
                        'chat_title': chat_title,
                        'thread_id': thread_id,
                        'sample_message': message_text,
                        'date': date
                    }
        
        if not topic_ids:
            print("❌ 没有找到包含话题ID的消息")
            print("💡 请确保:")
            print("   1. 在启用话题的频道中发送消息")
            print("   2. 机器人有权限接收消息")
            return
        
        print(f"\n✅ 找到 {len(topic_ids)} 个不同的话题:")
        
        for i, (key, info) in enumerate(topic_ids.items(), 1):
            print(f"\n📌 话题 {i}:")
            print(f"   频道: {info['chat_title']}")
            print(f"   频道ID: {info['chat_id']}")
            print(f"   话题ID: {info['thread_id']}")
            print(f"   示例消息: {info['sample_message']}...")
            print(f"   时间: {info['date'].strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 生成配置代码
        print(f"\n📝 配置代码示例:")
        print("```python")
        print('"topic_configs": {')
        
        for i, (key, info) in enumerate(topic_ids.items(), 1):
            topic_name = f"topic_{i}"
            print(f'    "{topic_name}": {{')
            print(f'        "chat_id": "{info["chat_id"]}",')
            print(f'        "thread_id": {info["thread_id"]},')
            print(f'        "description": "{info["chat_title"]} - 话题{i}"')
            print('    }' + (',' if i < len(topic_ids) else ''))
        
        print('}')
        print("```")
        
    except Exception as e:
        print(f"❌ 获取话题ID失败: {e}")

def main():
    """主函数"""
    print("🤖 Telegram话题ID获取工具")
    print("=" * 50)
    
    # 尝试从配置文件获取token
    try:
        from config import OUTPUT_CONFIG
        bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
        print(f"✅ 从配置文件获取Bot Token")
    except:
        bot_token = input("请输入Bot Token: ").strip()
    
    if not bot_token or bot_token == "YOUR_BOT_TOKEN_HERE":
        print("❌ 无效的Bot Token")
        return
    
    print("\n💡 使用说明:")
    print("1. 在目标频道的话题中发送一条消息")
    print("2. 运行此脚本获取话题ID")
    print("3. 将获取的ID配置到config.py中")
    
    input("\n按回车键开始获取话题ID...")
    
    get_topic_ids(bot_token)

if __name__ == "__main__":
    main()
