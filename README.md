# PYTHIA 智能分析系统

## 🎯 核心功能

### 1. AI对话机器人
- **文件**: `bot.py`
- **功能**: Telegram智能对话，支持群组和私聊
- **特性**: 对话记忆、自动群组检测、订阅管理

### 2. 市场分析报告
- **文件**: `pythia_integrated_complete.py`
- **功能**: PYTHIA代币市场分析和报告生成
- **特性**: 实时数据获取、HTML报告、图片转换、自动发送

## 🚀 快速启动

### 启动AI Bot
```bash
python bot.py
```

### 生成市场报告
```bash
python pythia_integrated_complete.py
```

## ⚙️ 配置

编辑 `config.py` 文件：

```python
# Telegram Bot配置
BOT_TOKEN = "你的Bot Token"
BOT_USERNAME = "pythia_is_ai_bot"

# Gemini AI配置  
GEMINI_API_KEY = "你的Gemini API Key"

# 自动群组模式
OUTPUT_CONFIG = {
    "telegram": {
        "bot_token": "你的Bot Token",
        "chat_id": "AUTO_DETECT",      # 自动检测群组
        "auto_group_mode": True,       # 开启自动群组
        "fallback_chat_id": "备用群组ID"
    }
}
```

## 📁 文件结构

```
pythia/
├── bot.py                         # AI对话机器人
├── pythia_integrated_complete.py  # 市场分析报告
├── config.py                      # 配置文件
├── requirements.txt               # 依赖包
├── memory.json                    # 对话记忆
├── data/                          # 数据目录
│   ├── active_groups.json         # 活跃群组
│   └── *.html                     # 报告文件
├── data_txt/                      # 文本数据导出
├── images/                        # 生成的图片
├── templates/                     # HTML模板
└── archive/                       # 归档文件
    ├── tests/                     # 测试文件
    ├── docs/                      # 文档文件
    ├── tools/                     # 工具文件
    ├── backups/                   # 备份文件
    └── others/                    # 其他文件
```

## 🔧 依赖安装

```bash
pip install -r requirements.txt
```

主要依赖：
- `python-telegram-bot` - Telegram Bot API
- `requests` - HTTP请求
- `selenium` - 网页自动化
- `Pillow` - 图片处理

## 💡 使用说明

### AI Bot使用
1. 将Bot添加到Telegram群组
2. 在群组中@Bot发送消息激活群组记录
3. 支持私聊和群组对话
4. 自动记忆对话历史

### 报告生成
1. 运行主程序自动获取市场数据
2. 生成HTML格式的分析报告
3. 转换为图片并发送到Telegram
4. 支持多群组自动发送

### 自动群组检测
- Bot在群组中被@时自动记录群组ID
- 报告生成时自动发送到所有活跃群组
- 无需手动配置群组ID

## 🛠️ 高级功能

- **数据导出**: 自动导出AI优化的文本格式数据
- **多群组管理**: 支持同时管理多个Telegram群组
- **智能过滤**: 高级市场数据过滤和分析
- **缓存机制**: 优化API请求性能
- **错误恢复**: 自动重试和备用机制

## 📊 监控和日志

- 运行日志自动记录到控制台
- 群组活动记录保存到 `data/active_groups.json`
- 对话记忆保存到 `memory.json`

## 🔒 安全说明

- 所有API密钥需要在 `config.py` 中配置
- 建议定期备份 `data/` 目录
- 敏感信息不要提交到版本控制

## 📞 支持

如有问题，请检查：
1. 配置文件是否正确设置
2. 网络连接是否正常
3. API密钥是否有效
4. Bot权限是否充足

---

**版本**: 2.0  
**更新**: 2025-08-02  
**功能**: AI对话 + 市场分析报告
