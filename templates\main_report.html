<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告</title>
    <style>
        body { font-family: Arial, sans-serif; background: #0D1117; color: #E6EDF3; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 20px; }
        .card { background: #161B22; border: 1px solid #30363D; border-radius: 8px; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐭 PYTHIA 分析报告</h1>
            <p>Token: {{token_address}} | Time: {{timestamp}}</p>
        </div>
        <div class="content">
            <div class="card">
                <h2>积极因素</h2>
                <ul>{{positive_factors}}</ul>
            </div>
            <div class="card">
                <h2>核心指标</h2>
                <p>市值: {{market_cap_millions}}M</p>
                <p>交易量: {{volume_24h_millions}}M</p>
                <p>流动性: {{liquidity_millions}}M</p>
            </div>
            <div class="card">
                <h2>交易对</h2>
                <table>{{pairs_table}}</table>
            </div>
        </div>
    </div>
</body>
</html>

    <script>
        // 导航系统JavaScript功能
        class NavigationSystem {
            constructor() {
                this.categories = this.loadCategories();
                this.currentEditingId = null;
                this.draggedElement = null;
                this.init();
            }

            // 初始化系统
            init() {
                this.renderCategories();
                this.setupEventListeners();
            }

            // 加载默认类别
            loadCategories() {
                const saved = localStorage.getItem('pythia_categories');
                if (saved) {
                    return JSON.parse(saved);
                }

                // 默认类别
                return [
                    { id: 'add', name: '添加类别', icon: '➕', type: 'special' },
                    { id: 'product', name: '产品', icon: '📦', type: 'normal' },
                    { id: 'creative', name: '创意', icon: '💡', type: 'normal' },
                    { id: 'programming', name: '编程', icon: '💻', type: 'normal' },
                    { id: 'analysis', name: '分析', icon: '📊', type: 'normal' },
                    { id: 'research', name: '研究', icon: '🔬', type: 'normal' },
                    { id: 'design', name: '设计', icon: '🎨', type: 'normal' },
                    { id: 'marketing', name: '营销', icon: '📈', type: 'normal' }
                ];
            }

            // 保存类别到本地存储
            saveCategories() {
                localStorage.setItem('pythia_categories', JSON.stringify(this.categories));
            }

            // 渲染类别按钮
            renderCategories() {
                const container = document.getElementById('navButtonsContainer');
                container.innerHTML = '';

                this.categories.forEach((category, index) => {
                    if (category.type === 'special') return; // 跳过特殊类别

                    const button = this.createCategoryButton(category, index);
                    container.appendChild(button);
                });
            }

            // 创建类别按钮
            createCategoryButton(category, index) {
                const button = document.createElement('div');
                button.className = 'nav-button';
                button.draggable = true;
                button.dataset.categoryId = category.id;
                button.dataset.index = index;

                button.innerHTML = `
                    <span class="nav-button-text">
                        <span style="margin-right: 8px;">${category.icon}</span>
                        ${category.name}
                    </span>
                    <div class="nav-button-actions">
                        <button class="action-btn" onclick="nav.editCategory('${category.id}')" title="编辑">
                            ✏️
                        </button>
                        <button class="action-btn" onclick="nav.deleteCategory('${category.id}')" title="删除">
                            🗑️
                        </button>
                    </div>
                `;

                // 添加点击事件
                button.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('action-btn')) {
                        this.selectCategory(category.id);
                    }
                });

                // 添加拖拽事件
                this.setupDragEvents(button);

                return button;
            }

            // 设置拖拽事件
            setupDragEvents(button) {
                button.addEventListener('dragstart', (e) => {
                    this.draggedElement = button;
                    button.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                });

                button.addEventListener('dragend', () => {
                    button.classList.remove('dragging');
                    this.draggedElement = null;
                    this.clearDragPlaceholders();
                });

                button.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    this.showDragPlaceholder(button);
                });

                button.addEventListener('drop', (e) => {
                    e.preventDefault();
                    if (this.draggedElement && this.draggedElement !== button) {
                        this.reorderCategories(this.draggedElement, button);
                    }
                });
            }

            // 显示拖拽占位符
            showDragPlaceholder(targetButton) {
                this.clearDragPlaceholders();
                const placeholder = document.createElement('div');
                placeholder.className = 'drag-placeholder show';
                targetButton.parentNode.insertBefore(placeholder, targetButton);
            }

            // 清除拖拽占位符
            clearDragPlaceholders() {
                const placeholders = document.querySelectorAll('.drag-placeholder');
                placeholders.forEach(p => p.remove());
            }

            // 重新排序类别
            reorderCategories(draggedButton, targetButton) {
                const draggedIndex = parseInt(draggedButton.dataset.index);
                const targetIndex = parseInt(targetButton.dataset.index);

                // 移动数组中的元素
                const draggedCategory = this.categories.splice(draggedIndex, 1)[0];
                this.categories.splice(targetIndex, 0, draggedCategory);

                this.saveCategories();
                this.renderCategories();
            }

            // 选择类别
            selectCategory(categoryId) {
                // 移除所有活跃状态
                document.querySelectorAll('.nav-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // 添加活跃状态到当前按钮
                const button = document.querySelector(`[data-category-id="${categoryId}"]`);
                if (button) {
                    button.classList.add('active');
                }

                // 触发类别切换事件
                this.onCategoryChange(categoryId);
            }

            // 类别切换回调
            onCategoryChange(categoryId) {
                console.log('切换到类别:', categoryId);
                // 这里可以添加具体的类别切换逻辑
                // 例如：加载不同的内容、更新图表等
            }